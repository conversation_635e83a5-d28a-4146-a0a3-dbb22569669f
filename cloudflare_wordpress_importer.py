#!/usr/bin/env python3
"""
Comprehensive WordPress to Cloudflare Services Importer
Imports WordPress database content to Cloudflare D1, KV, and R2 while excluding author information
"""

import re
import json
import html
import requests
import sqlite3
from datetime import datetime
from urllib.parse import unquote
import os
import sys

# Cloudflare Configuration
CLOUDFLARE_ACCOUNT_ID = "ab54ca2d01df4886aa0c3f240ace806d"
DATABASE_ID = "7e82e5e1-b4d3-4607-bf5a-6874e4a4d9b4"  # wordpress-content database
API_TOKEN = os.getenv('CLOUDFLARE_API_TOKEN', 'your_api_token_here')

class CloudflareWordPressImporter:
    def __init__(self):
        self.api_token = API_TOKEN
        self.account_id = CLOUDFLARE_ACCOUNT_ID
        self.database_id = DATABASE_ID
        self.base_url = f"https://api.cloudflare.com/client/v4/accounts/{self.account_id}"
        
        if not self.api_token or self.api_token == 'your_api_token_here':
            print("❌ Please set CLOUDFLARE_API_TOKEN environment variable")
            sys.exit(1)
    
    def execute_d1_query(self, sql, params=None):
        """Execute a query against Cloudflare D1 database"""
        url = f"{self.base_url}/d1/database/{self.database_id}/query"
        
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'sql': sql,
            'params': params or []
        }
        
        try:
            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ D1 Query failed: {response.text}")
                return None
        except Exception as e:
            print(f"❌ D1 Query error: {e}")
            return None
    
    def create_database_schema(self):
        """Create the complete database schema in Cloudflare D1"""
        print("🏗️  Creating database schema...")
        
        # Posts table (excluding author information)
        posts_schema = """
        CREATE TABLE IF NOT EXISTS posts (
            id INTEGER PRIMARY KEY,
            post_date DATETIME NOT NULL,
            post_date_gmt DATETIME NOT NULL,
            post_content TEXT NOT NULL,
            post_title TEXT NOT NULL,
            post_excerpt TEXT NOT NULL,
            post_status VARCHAR(20) NOT NULL DEFAULT 'publish',
            comment_status VARCHAR(20) NOT NULL DEFAULT 'open',
            ping_status VARCHAR(20) NOT NULL DEFAULT 'open',
            post_password VARCHAR(255) NOT NULL DEFAULT '',
            post_name VARCHAR(200) NOT NULL DEFAULT '',
            post_modified DATETIME NOT NULL,
            post_modified_gmt DATETIME NOT NULL,
            post_parent INTEGER NOT NULL DEFAULT 0,
            guid VARCHAR(255) NOT NULL DEFAULT '',
            menu_order INTEGER NOT NULL DEFAULT 0,
            post_type VARCHAR(20) NOT NULL DEFAULT 'post',
            post_mime_type VARCHAR(100) NOT NULL DEFAULT '',
            comment_count INTEGER NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # Comments table (excluding user_id and author personal info)
        comments_schema = """
        CREATE TABLE IF NOT EXISTS comments (
            id INTEGER PRIMARY KEY,
            post_id INTEGER NOT NULL,
            comment_author TEXT NOT NULL,
            comment_date DATETIME NOT NULL,
            comment_date_gmt DATETIME NOT NULL,
            comment_content TEXT NOT NULL,
            comment_karma INTEGER NOT NULL DEFAULT 0,
            comment_approved VARCHAR(20) NOT NULL DEFAULT '1',
            comment_type VARCHAR(20) NOT NULL DEFAULT 'comment',
            comment_parent INTEGER NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES posts(id)
        )
        """
        
        # Terms table (categories, tags)
        terms_schema = """
        CREATE TABLE IF NOT EXISTS terms (
            id INTEGER PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            slug VARCHAR(200) NOT NULL,
            term_group INTEGER NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # Term taxonomy table
        term_taxonomy_schema = """
        CREATE TABLE IF NOT EXISTS term_taxonomy (
            id INTEGER PRIMARY KEY,
            term_id INTEGER NOT NULL,
            taxonomy VARCHAR(32) NOT NULL,
            description TEXT NOT NULL,
            parent INTEGER NOT NULL DEFAULT 0,
            count INTEGER NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (term_id) REFERENCES terms(id)
        )
        """
        
        # Term relationships table
        term_relationships_schema = """
        CREATE TABLE IF NOT EXISTS term_relationships (
            object_id INTEGER NOT NULL,
            term_taxonomy_id INTEGER NOT NULL,
            term_order INTEGER NOT NULL DEFAULT 0,
            PRIMARY KEY (object_id, term_taxonomy_id),
            FOREIGN KEY (object_id) REFERENCES posts(id),
            FOREIGN KEY (term_taxonomy_id) REFERENCES term_taxonomy(id)
        )
        """
        
        # Post metadata table
        postmeta_schema = """
        CREATE TABLE IF NOT EXISTS postmeta (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            post_id INTEGER NOT NULL,
            meta_key VARCHAR(255),
            meta_value TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES posts(id)
        )
        """
        
        # Comment metadata table
        commentmeta_schema = """
        CREATE TABLE IF NOT EXISTS commentmeta (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            comment_id INTEGER NOT NULL,
            meta_key VARCHAR(255),
            meta_value TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (comment_id) REFERENCES comments(id)
        )
        """
        
        # Term metadata table
        termmeta_schema = """
        CREATE TABLE IF NOT EXISTS termmeta (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            term_id INTEGER NOT NULL,
            meta_key VARCHAR(255),
            meta_value TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (term_id) REFERENCES terms(id)
        )
        """
        
        schemas = [
            posts_schema,
            comments_schema,
            terms_schema,
            term_taxonomy_schema,
            term_relationships_schema,
            postmeta_schema,
            commentmeta_schema,
            termmeta_schema
        ]
        
        for schema in schemas:
            result = self.execute_d1_query(schema)
            if result:
                print(f"✅ Created table successfully")
            else:
                print(f"❌ Failed to create table")
                return False
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(post_status)",
            "CREATE INDEX IF NOT EXISTS idx_posts_type ON posts(post_type)",
            "CREATE INDEX IF NOT EXISTS idx_posts_name ON posts(post_name)",
            "CREATE INDEX IF NOT EXISTS idx_posts_date ON posts(post_date)",
            "CREATE INDEX IF NOT EXISTS idx_comments_post ON comments(post_id)",
            "CREATE INDEX IF NOT EXISTS idx_comments_approved ON comments(comment_approved)",
            "CREATE INDEX IF NOT EXISTS idx_terms_slug ON terms(slug)",
            "CREATE INDEX IF NOT EXISTS idx_taxonomy_term ON term_taxonomy(term_id)",
            "CREATE INDEX IF NOT EXISTS idx_taxonomy_type ON term_taxonomy(taxonomy)",
            "CREATE INDEX IF NOT EXISTS idx_postmeta_post ON postmeta(post_id)",
            "CREATE INDEX IF NOT EXISTS idx_postmeta_key ON postmeta(meta_key)",
            "CREATE INDEX IF NOT EXISTS idx_commentmeta_comment ON commentmeta(comment_id)",
            "CREATE INDEX IF NOT EXISTS idx_termmeta_term ON termmeta(term_id)"
        ]
        
        for index in indexes:
            self.execute_d1_query(index)
        
        print("✅ Database schema created successfully!")
        return True

    def parse_sql_values(self, values_string):
        """Parse SQL VALUES string into individual values"""
        values = []
        current_value = ""
        in_quotes = False
        quote_char = None
        i = 0

        while i < len(values_string):
            char = values_string[i]

            if not in_quotes:
                if char in ["'", '"']:
                    in_quotes = True
                    quote_char = char
                    current_value = ""
                elif char == ',':
                    values.append(current_value.strip())
                    current_value = ""
                else:
                    current_value += char
            else:
                if char == quote_char:
                    # Check if it's escaped
                    if i + 1 < len(values_string) and values_string[i + 1] == quote_char:
                        current_value += char
                        i += 1  # Skip the next quote
                    else:
                        in_quotes = False
                        quote_char = None
                else:
                    current_value += char

            i += 1

        # Add the last value
        if current_value.strip():
            values.append(current_value.strip())

        return values

    def clean_text(self, text):
        """Clean and decode text from SQL"""
        if not text or text == 'NULL':
            return ""

        # Remove quotes
        text = text.strip("'\"")

        # Decode HTML entities
        text = html.unescape(text)

        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def extract_posts_from_sql(self, sql_file_path):
        """Extract WordPress posts from SQL dump file"""
        print("📖 Extracting posts from SQL dump...")
        posts = []

        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Find the wp_posts INSERT statements
        post_pattern = r"INSERT INTO `wp_posts` VALUES\s*\((.*?)\);"
        matches = re.findall(post_pattern, content, re.DOTALL)

        for match in matches:
            # Split the values by comma, handling quoted strings
            values = self.parse_sql_values(match)

            if len(values) >= 23:  # WordPress posts table has many columns
                post_id = values[0]
                # Skip post_author (values[1]) - excluding author information
                post_date = values[2]
                post_date_gmt = values[3]
                post_content = self.clean_text(values[4])
                post_title = self.clean_text(values[5])
                post_excerpt = self.clean_text(values[6])
                post_status = self.clean_text(values[7])
                comment_status = self.clean_text(values[8])
                ping_status = self.clean_text(values[9])
                post_password = self.clean_text(values[10])
                post_name = self.clean_text(values[11])
                # Skip to_ping and pinged
                post_modified = values[14]
                post_modified_gmt = values[15]
                # Skip post_content_filtered
                post_parent = values[17]
                guid = self.clean_text(values[18])
                menu_order = values[19]
                post_type = self.clean_text(values[20])
                post_mime_type = self.clean_text(values[21])
                comment_count = values[22]

                posts.append({
                    'id': int(post_id),
                    'post_date': post_date.strip("'\""),
                    'post_date_gmt': post_date_gmt.strip("'\""),
                    'post_content': post_content,
                    'post_title': post_title,
                    'post_excerpt': post_excerpt,
                    'post_status': post_status,
                    'comment_status': comment_status,
                    'ping_status': ping_status,
                    'post_password': post_password,
                    'post_name': post_name,
                    'post_modified': post_modified.strip("'\""),
                    'post_modified_gmt': post_modified_gmt.strip("'\""),
                    'post_parent': int(post_parent) if post_parent != 'NULL' else 0,
                    'guid': guid,
                    'menu_order': int(menu_order) if menu_order != 'NULL' else 0,
                    'post_type': post_type,
                    'post_mime_type': post_mime_type,
                    'comment_count': int(comment_count) if comment_count != 'NULL' else 0
                })

        print(f"✅ Extracted {len(posts)} posts")
        return posts

if __name__ == "__main__":
    importer = CloudflareWordPressImporter()

    # Create database schema
    if not importer.create_database_schema():
        print("❌ Failed to create database schema")
        sys.exit(1)

    # Extract and import data
    sql_file = "/Users/<USER>/Desktop/SH_WEB/u957990218_GpBKT.zayotech-com.20250727190356.sql"

    def extract_comments_from_sql(self, sql_file_path):
        """Extract WordPress comments from SQL dump file (excluding user_id)"""
        print("💬 Extracting comments from SQL dump...")
        comments = []

        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Find the wp_comments INSERT statements
        comment_pattern = r"INSERT INTO `wp_comments` VALUES\s*\((.*?)\);"
        matches = re.findall(comment_pattern, content, re.DOTALL)

        for match in matches:
            values = self.parse_sql_values(match)

            if len(values) >= 15:
                comment_id = values[0]
                comment_post_id = values[1]
                comment_author = self.clean_text(values[2])
                # Skip comment_author_email, comment_author_url, comment_author_IP (values[3-5])
                comment_date = values[6]
                comment_date_gmt = values[7]
                comment_content = self.clean_text(values[8])
                comment_karma = values[9]
                comment_approved = self.clean_text(values[10])
                comment_type = self.clean_text(values[12])
                comment_parent = values[13]
                # Skip user_id (values[14]) - excluding author information

                comments.append({
                    'id': int(comment_id),
                    'post_id': int(comment_post_id),
                    'comment_author': comment_author,
                    'comment_date': comment_date.strip("'\""),
                    'comment_date_gmt': comment_date_gmt.strip("'\""),
                    'comment_content': comment_content,
                    'comment_karma': int(comment_karma) if comment_karma != 'NULL' else 0,
                    'comment_approved': comment_approved,
                    'comment_type': comment_type,
                    'comment_parent': int(comment_parent) if comment_parent != 'NULL' else 0
                })

        print(f"✅ Extracted {len(comments)} comments")
        return comments

    def extract_terms_from_sql(self, sql_file_path):
        """Extract WordPress terms (categories/tags) from SQL dump file"""
        print("🏷️  Extracting terms from SQL dump...")
        terms = []

        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Find the wp_terms INSERT statements
        term_pattern = r"INSERT INTO `wp_terms` VALUES\s*\((.*?)\);"
        matches = re.findall(term_pattern, content, re.DOTALL)

        for match in matches:
            values = self.parse_sql_values(match)

            if len(values) >= 4:
                term_id = values[0]
                name = self.clean_text(values[1])
                slug = self.clean_text(values[2])
                term_group = values[3]

                terms.append({
                    'id': int(term_id),
                    'name': name,
                    'slug': slug,
                    'term_group': int(term_group) if term_group != 'NULL' else 0
                })

        print(f"✅ Extracted {len(terms)} terms")
        return terms

    def extract_term_taxonomy_from_sql(self, sql_file_path):
        """Extract WordPress term taxonomy from SQL dump file"""
        print("📂 Extracting term taxonomy from SQL dump...")
        taxonomies = []

        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Find the wp_term_taxonomy INSERT statements
        taxonomy_pattern = r"INSERT INTO `wp_term_taxonomy` VALUES\s*\((.*?)\);"
        matches = re.findall(taxonomy_pattern, content, re.DOTALL)

        for match in matches:
            values = self.parse_sql_values(match)

            if len(values) >= 6:
                term_taxonomy_id = values[0]
                term_id = values[1]
                taxonomy = self.clean_text(values[2])
                description = self.clean_text(values[3])
                parent = values[4]
                count = values[5]

                taxonomies.append({
                    'id': int(term_taxonomy_id),
                    'term_id': int(term_id),
                    'taxonomy': taxonomy,
                    'description': description,
                    'parent': int(parent) if parent != 'NULL' else 0,
                    'count': int(count) if count != 'NULL' else 0
                })

        print(f"✅ Extracted {len(taxonomies)} term taxonomies")
        return taxonomies

    def extract_term_relationships_from_sql(self, sql_file_path):
        """Extract WordPress term relationships from SQL dump file"""
        print("🔗 Extracting term relationships from SQL dump...")
        relationships = []

        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Find the wp_term_relationships INSERT statements
        relationship_pattern = r"INSERT INTO `wp_term_relationships` VALUES\s*\((.*?)\);"
        matches = re.findall(relationship_pattern, content, re.DOTALL)

        for match in matches:
            values = self.parse_sql_values(match)

            if len(values) >= 3:
                object_id = values[0]
                term_taxonomy_id = values[1]
                term_order = values[2]

                relationships.append({
                    'object_id': int(object_id),
                    'term_taxonomy_id': int(term_taxonomy_id),
                    'term_order': int(term_order) if term_order != 'NULL' else 0
                })

        print(f"✅ Extracted {len(relationships)} term relationships")
        return relationships

    def import_posts_to_d1(self, posts):
        """Import posts to Cloudflare D1"""
        print("📝 Importing posts to D1...")

        query = """
        INSERT INTO posts (
            id, post_date, post_date_gmt, post_content, post_title, post_excerpt,
            post_status, comment_status, ping_status, post_password, post_name,
            post_modified, post_modified_gmt, post_parent, guid, menu_order,
            post_type, post_mime_type, comment_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        success_count = 0
        for post in posts:
            params = [
                post['id'], post['post_date'], post['post_date_gmt'],
                post['post_content'], post['post_title'], post['post_excerpt'],
                post['post_status'], post['comment_status'], post['ping_status'],
                post['post_password'], post['post_name'], post['post_modified'],
                post['post_modified_gmt'], post['post_parent'], post['guid'],
                post['menu_order'], post['post_type'], post['post_mime_type'],
                post['comment_count']
            ]

            result = self.execute_d1_query(query, params)
            if result:
                success_count += 1
            else:
                print(f"❌ Failed to import post: {post['post_title']}")

        print(f"✅ Imported {success_count}/{len(posts)} posts")
        return success_count

    def import_comments_to_d1(self, comments):
        """Import comments to Cloudflare D1"""
        print("💬 Importing comments to D1...")

        query = """
        INSERT INTO comments (
            id, post_id, comment_author, comment_date, comment_date_gmt,
            comment_content, comment_karma, comment_approved, comment_type, comment_parent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        success_count = 0
        for comment in comments:
            params = [
                comment['id'], comment['post_id'], comment['comment_author'],
                comment['comment_date'], comment['comment_date_gmt'],
                comment['comment_content'], comment['comment_karma'],
                comment['comment_approved'], comment['comment_type'], comment['comment_parent']
            ]

            result = self.execute_d1_query(query, params)
            if result:
                success_count += 1

        print(f"✅ Imported {success_count}/{len(comments)} comments")
        return success_count

    def import_terms_to_d1(self, terms):
        """Import terms to Cloudflare D1"""
        print("🏷️  Importing terms to D1...")

        query = """
        INSERT INTO terms (id, name, slug, term_group) VALUES (?, ?, ?, ?)
        """

        success_count = 0
        for term in terms:
            params = [term['id'], term['name'], term['slug'], term['term_group']]

            result = self.execute_d1_query(query, params)
            if result:
                success_count += 1

        print(f"✅ Imported {success_count}/{len(terms)} terms")
        return success_count

    def import_term_taxonomy_to_d1(self, taxonomies):
        """Import term taxonomy to Cloudflare D1"""
        print("📂 Importing term taxonomy to D1...")

        query = """
        INSERT INTO term_taxonomy (id, term_id, taxonomy, description, parent, count)
        VALUES (?, ?, ?, ?, ?, ?)
        """

        success_count = 0
        for taxonomy in taxonomies:
            params = [
                taxonomy['id'], taxonomy['term_id'], taxonomy['taxonomy'],
                taxonomy['description'], taxonomy['parent'], taxonomy['count']
            ]

            result = self.execute_d1_query(query, params)
            if result:
                success_count += 1

        print(f"✅ Imported {success_count}/{len(taxonomies)} term taxonomies")
        return success_count

    def import_term_relationships_to_d1(self, relationships):
        """Import term relationships to Cloudflare D1"""
        print("🔗 Importing term relationships to D1...")

        query = """
        INSERT INTO term_relationships (object_id, term_taxonomy_id, term_order)
        VALUES (?, ?, ?)
        """

        success_count = 0
        for relationship in relationships:
            params = [
                relationship['object_id'], relationship['term_taxonomy_id'],
                relationship['term_order']
            ]

            result = self.execute_d1_query(query, params)
            if result:
                success_count += 1

        print(f"✅ Imported {success_count}/{len(relationships)} term relationships")
        return success_count

    # Extract all data
    print("🔍 Starting data extraction...")
    posts = importer.extract_posts_from_sql(sql_file)
    comments = importer.extract_comments_from_sql(sql_file)
    terms = importer.extract_terms_from_sql(sql_file)
    taxonomies = importer.extract_term_taxonomy_from_sql(sql_file)
    relationships = importer.extract_term_relationships_from_sql(sql_file)

    print(f"\n📊 Extraction Summary:")
    print(f"   Posts: {len(posts)}")
    print(f"   Comments: {len(comments)}")
    print(f"   Terms: {len(terms)}")
    print(f"   Taxonomies: {len(taxonomies)}")
    print(f"   Relationships: {len(relationships)}")

    # Import all data to D1
    print("\n🚀 Starting data import to Cloudflare D1...")

    # Import in order to maintain referential integrity
    importer.import_posts_to_d1(posts)
    importer.import_comments_to_d1(comments)
    importer.import_terms_to_d1(terms)
    importer.import_term_taxonomy_to_d1(taxonomies)
    importer.import_term_relationships_to_d1(relationships)

    print("\n🎉 WordPress import completed successfully!")
    print("✅ All content imported to Cloudflare D1 (excluding author information)")
    print(f"📍 Database ID: {importer.database_id}")

    # Show some sample queries
    print("\n📋 Sample queries to verify import:")
    print("   SELECT COUNT(*) FROM posts;")
    print("   SELECT COUNT(*) FROM comments;")
    print("   SELECT COUNT(*) FROM terms;")
    print("   SELECT post_title, post_status FROM posts LIMIT 5;")
