-- Initial data for the blog system

-- Insert default categories
INSERT INTO categories (name, slug, description, sort_order) VALUES
('AI', 'ai', 'Artificial Intelligence and Machine Learning content', 1),
('Programming', 'programming', 'Programming tutorials and guides', 2),
('Tutorials', 'tutorials', 'Step-by-step tutorials and how-to guides', 3),
('Technology', 'technology', 'Latest technology news and insights', 4),
('Web Development', 'web-development', 'Web development tutorials and best practices', 5),
('JavaScript', 'javascript', 'JavaScript programming and frameworks', 6),
('React', 'react', 'React.js tutorials and guides', 7),
('Next.js', 'nextjs', 'Next.js framework tutorials', 8),
('CSS', 'css', 'CSS styling and design tutorials', 9),
('Database', 'database', 'Database design and management', 10);

-- Insert default tags
INSERT INTO tags (name, slug, description) VALUES
('beginner', 'beginner', 'Content suitable for beginners'),
('advanced', 'advanced', 'Advanced level content'),
('tutorial', 'tutorial', 'Tutorial content'),
('guide', 'guide', 'Comprehensive guides'),
('tips', 'tips', 'Quick tips and tricks'),
('best-practices', 'best-practices', 'Industry best practices'),
('performance', 'performance', 'Performance optimization'),
('security', 'security', 'Security-related content'),
('responsive', 'responsive', 'Responsive design'),
('mobile', 'mobile', 'Mobile development');

-- Insert default admin user (password: admin123 - should be changed in production)
INSERT INTO users (username, email, password_hash, display_name, role, email_verified, bio, avatar_url) VALUES
('admin', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqO', 'Administrator', 'admin', TRUE, 'Blog administrator and content creator', '/profile-image.jpg');

-- Insert default site settings
INSERT INTO settings (key, value) VALUES
('site_title', 'Teknorial Blog'),
('site_description', 'Technology tutorials and programming guides'),
('site_url', 'https://teknorial.com'),
('posts_per_page', '12'),
('comment_moderation', 'true'),
('default_language', 'en'),
('theme', 'teknorial'),
('timezone', 'UTC');

-- Insert sample blog posts
INSERT INTO posts (title, slug, content, excerpt, author_id, status, featured_image_url, language, published_at, view_count, comment_count) VALUES
('Getting Started with Next.js 15: A Complete Guide', 'getting-started-nextjs-15-complete-guide', 
'# Getting Started with Next.js 15

Next.js 15 brings exciting new features and improvements that make building React applications even more powerful and efficient. In this comprehensive guide, we''ll explore everything you need to know to get started with Next.js 15.

## What''s New in Next.js 15

Next.js 15 introduces several groundbreaking features:

- **Improved App Router**: Enhanced routing capabilities with better performance
- **Server Components**: Better server-side rendering and data fetching
- **Turbopack**: Faster build times and development experience
- **Enhanced Image Optimization**: Better image loading and optimization

## Installation and Setup

To get started with Next.js 15, you can create a new project using:

```bash
npx create-next-app@latest my-app
cd my-app
npm run dev
```

## Key Features

### 1. App Router
The App Router provides a more intuitive way to structure your application with file-based routing.

### 2. Server Components
Server Components allow you to render components on the server, reducing client-side JavaScript and improving performance.

### 3. Data Fetching
Next.js 15 provides powerful data fetching capabilities with built-in caching and revalidation.

## Conclusion

Next.js 15 is a significant step forward in React development, offering improved performance, better developer experience, and powerful new features that make building modern web applications easier than ever.',
'Learn how to build modern web applications with Next.js 15, including the latest features like Server Components, App Router, and improved performance optimizations.',
1, 'published', '/blog-images/web-architecture.jpg', 'en', '2025-01-25 10:00:00', 1250, 23),

('Understanding React Server Components', 'understanding-react-server-components',
'# Understanding React Server Components

React Server Components represent a paradigm shift in how we think about React applications. They allow us to render components on the server, reducing the amount of JavaScript sent to the client and improving performance.

## What are Server Components?

Server Components are React components that run on the server and send their rendered output to the client. Unlike traditional React components that run in the browser, Server Components execute on the server during the build process or at request time.

## Benefits of Server Components

### 1. Reduced Bundle Size
Since Server Components run on the server, their code doesn''t need to be sent to the client, resulting in smaller JavaScript bundles.

### 2. Better Performance
Server Components can access server-side resources directly, eliminating the need for client-server round trips for data fetching.

### 3. Improved SEO
Content rendered by Server Components is available immediately when the page loads, improving SEO and initial page load times.

## How to Use Server Components

In Next.js 13+, components in the `app` directory are Server Components by default:

```jsx
// This is a Server Component by default
export default function BlogPost({ id }) {
  // This runs on the server
  const post = await fetchPost(id);
  
  return (
    <article>
      <h1>{post.title}</h1>
      <p>{post.content}</p>
    </article>
  );
}
```

## Client Components

When you need interactivity, you can use Client Components by adding the `"use client"` directive:

```jsx
"use client";

import { useState } from "react";

export default function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <button onClick={() => setCount(count + 1)}>
      Count: {count}
    </button>
  );
}
```

## Best Practices

1. Use Server Components for static content and data fetching
2. Use Client Components for interactive features
3. Keep the client-server boundary clear
4. Minimize the use of Client Components when possible

## Conclusion

React Server Components are a powerful addition to the React ecosystem, enabling better performance and user experience. Understanding when and how to use them is crucial for modern React development.',
'Dive deep into React Server Components and learn how they can improve your application''s performance and user experience.',
1, 'published', '/blog-images/javascript-techniques.jpg', 'en', '2025-01-24 14:30:00', 890, 15),

('AI-Powered Development Tools in 2025', 'ai-powered-development-tools-2025',
'# AI-Powered Development Tools in 2025

The landscape of software development is rapidly evolving with the integration of artificial intelligence. In 2025, AI-powered tools are not just helpful additions to a developer''s toolkit—they''re becoming essential for staying competitive and productive.

## The AI Revolution in Development

AI has transformed how we approach software development, from code generation to testing and deployment. These tools are making developers more productive and helping teams deliver better software faster.

## Top AI Development Tools

### 1. GitHub Copilot
GitHub Copilot continues to lead the AI coding assistant space, offering:
- Intelligent code completion
- Function generation from comments
- Code explanation and documentation
- Multi-language support

### 2. ChatGPT and GPT-4
Large language models have become invaluable for:
- Code review and debugging
- Architecture planning
- Documentation writing
- Learning new technologies

### 3. Tabnine
Tabnine provides:
- AI-powered code completion
- Team-specific model training
- Privacy-focused AI assistance
- IDE integration

### 4. Replit Ghostwriter
Replit''s AI assistant offers:
- Real-time code suggestions
- Code explanation
- Bug detection and fixes
- Project scaffolding

## AI in Testing and Quality Assurance

### Automated Test Generation
AI tools can now generate comprehensive test suites by analyzing your codebase and identifying potential edge cases.

### Bug Detection
Advanced AI systems can detect bugs and security vulnerabilities before they reach production.

### Code Review
AI-powered code review tools can identify code smells, suggest improvements, and ensure coding standards compliance.

## AI in DevOps and Deployment

### Intelligent Monitoring
AI-powered monitoring tools can predict system failures and automatically scale resources based on usage patterns.

### Automated Deployment
AI can optimize deployment strategies and rollback procedures based on historical data and current system state.

## Best Practices for AI-Assisted Development

1. **Understand the Limitations**: AI tools are assistants, not replacements for human judgment
2. **Review AI-Generated Code**: Always review and test AI-generated code thoroughly
3. **Maintain Security**: Be cautious about sharing sensitive code with AI tools
4. **Stay Updated**: AI tools evolve rapidly; keep up with new features and capabilities
5. **Combine Tools**: Use multiple AI tools for different aspects of development

## The Future of AI in Development

As we look ahead, we can expect:
- More sophisticated code generation
- Better understanding of project context
- Improved debugging and optimization
- Enhanced collaboration between AI and human developers

## Conclusion

AI-powered development tools are reshaping how we build software. By embracing these tools while maintaining good development practices, developers can significantly improve their productivity and code quality. The key is to view AI as a powerful assistant that enhances human capabilities rather than replacing them.',
'Explore the latest AI tools that are revolutionizing software development, from code generation to automated testing and deployment.',
1, 'published', '/blog-images/html-fundamentals.jpg', 'en', '2025-01-23 09:15:00', 2100, 42),

('Building Responsive Layouts with CSS Grid', 'building-responsive-layouts-css-grid',
'# Building Responsive Layouts with CSS Grid

CSS Grid has revolutionized how we create layouts on the web. It provides a powerful two-dimensional layout system that makes creating complex, responsive designs easier than ever before.

## What is CSS Grid?

CSS Grid Layout is a two-dimensional layout method that allows you to create complex layouts with rows and columns. Unlike Flexbox, which is primarily one-dimensional, Grid excels at creating layouts in both dimensions simultaneously.

## Basic Grid Concepts

### Grid Container and Grid Items
- **Grid Container**: The parent element with `display: grid`
- **Grid Items**: The direct children of the grid container

### Grid Lines and Tracks
- **Grid Lines**: The dividing lines that make up the structure of the grid
- **Grid Tracks**: The space between two adjacent grid lines

## Creating Your First Grid

```css
.grid-container {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: auto 1fr auto;
  gap: 20px;
  min-height: 100vh;
}
```

## Responsive Grid Layouts

### Using Auto-Fit and Minmax
```css
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}
```

### Media Queries with Grid
```css
.grid-container {
  display: grid;
  gap: 20px;
}

@media (min-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-container {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## Advanced Grid Techniques

### Grid Areas
```css
.layout {
  display: grid;
  grid-template-areas:
    "header header header"
    "sidebar main aside"
    "footer footer footer";
  grid-template-columns: 200px 1fr 200px;
  grid-template-rows: auto 1fr auto;
}

.header { grid-area: header; }
.sidebar { grid-area: sidebar; }
.main { grid-area: main; }
.aside { grid-area: aside; }
.footer { grid-area: footer; }
```

## Best Practices

1. **Start Simple**: Begin with basic grid layouts and gradually add complexity
2. **Use Semantic HTML**: Ensure your HTML structure makes sense without CSS
3. **Consider Accessibility**: Test your layouts with screen readers
4. **Progressive Enhancement**: Provide fallbacks for older browsers
5. **Test Across Devices**: Ensure your grid works on all screen sizes

## Common Grid Patterns

### Card Layout
```css
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}
```

### Holy Grail Layout
```css
.holy-grail {
  display: grid;
  grid-template-areas:
    "header header header"
    "nav main aside"
    "footer footer footer";
  grid-template-columns: 150px 1fr 150px;
  grid-template-rows: auto 1fr auto;
  min-height: 100vh;
}
```

## Conclusion

CSS Grid is a powerful tool for creating modern, responsive layouts. By understanding its core concepts and practicing with different patterns, you can create sophisticated layouts that work beautifully across all devices.',
'Master CSS Grid to create complex, responsive layouts that work perfectly across all devices and screen sizes.',
1, 'published', '/blog-images/css-animations.jpg', 'en', '2025-01-22 16:45:00', 675, 18),

('TypeScript Best Practices for Large Applications', 'typescript-best-practices-large-applications',
'# TypeScript Best Practices for Large Applications

As applications grow in size and complexity, TypeScript becomes increasingly valuable for maintaining code quality and developer productivity. This guide covers essential best practices for using TypeScript in large-scale applications.

## Project Structure and Organization

### Modular Architecture
Organize your code into well-defined modules with clear boundaries:

```typescript
// types/user.ts
export interface User {
  id: string;
  email: string;
  profile: UserProfile;
}

// services/userService.ts
import { User } from "../types/user";

export class UserService {
  async getUser(id: string): Promise<User> {
    // Implementation
  }
}
```

### Barrel Exports
Use index files to create clean import paths:

```typescript
// types/index.ts
export * from "./user";
export * from "./post";
export * from "./comment";

// Usage
import { User, Post, Comment } from "../types";
```

## Type Safety Best Practices

### Strict TypeScript Configuration
Enable strict mode in your `tsconfig.json`:

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noImplicitThis": true
  }
}
```

### Utility Types
Leverage TypeScript''s built-in utility types:

```typescript
interface User {
  id: string;
  name: string;
  email: string;
  password: string;
}

// Create types for different use cases
type PublicUser = Omit<User, "password">;
type UserUpdate = Partial<Pick<User, "name" | "email">>;
type CreateUser = Omit<User, "id">;
```

## Advanced Type Patterns

### Discriminated Unions
Use discriminated unions for type-safe state management:

```typescript
type LoadingState = {
  status: "loading";
};

type SuccessState = {
  status: "success";
  data: User[];
};

type ErrorState = {
  status: "error";
  error: string;
};

type ApiState = LoadingState | SuccessState | ErrorState;

function handleApiState(state: ApiState) {
  switch (state.status) {
    case "loading":
      return "Loading...";
    case "success":
      return state.data.map(user => user.name);
    case "error":
      return `Error: ${state.error}`;
  }
}
```

### Generic Constraints
Use generic constraints for flexible, type-safe APIs:

```typescript
interface Identifiable {
  id: string;
}

function updateEntity<T extends Identifiable>(
  entity: T,
  updates: Partial<Omit<T, "id">>
): T {
  return { ...entity, ...updates };
}
```

## Error Handling

### Result Pattern
Implement a Result pattern for better error handling:

```typescript
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };

async function fetchUser(id: string): Promise<Result<User>> {
  try {
    const user = await userService.getUser(id);
    return { success: true, data: user };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}
```

## Performance Considerations

### Type-Only Imports
Use type-only imports when you only need types:

```typescript
import type { User } from "./types/user";
import { validateUser } from "./utils/validation";
```

### Lazy Loading Types
Use dynamic imports for large type definitions:

```typescript
async function processLargeData() {
  const { LargeDataType } = await import("./types/largeData");
  // Use LargeDataType
}
```

## Testing with TypeScript

### Type Testing
Test your types with tools like `tsd`:

```typescript
import { expectType } from "tsd";
import { User, createUser } from "./user";

expectType<User>(createUser({ name: "John", email: "<EMAIL>" }));
```

## Code Quality Tools

### ESLint Configuration
Use TypeScript-specific ESLint rules:

```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn"
  }
}
```

## Conclusion

Following these TypeScript best practices will help you build maintainable, scalable applications. The key is to leverage TypeScript''s type system to catch errors early and provide better developer experience while maintaining code that''s easy to understand and modify.',
'Learn essential TypeScript patterns and practices for building maintainable, scalable applications in enterprise environments.',
1, 'published', '/blog-images/html-images.jpg', 'en', '2025-01-21 11:20:00', 1450, 31),

('Modern Database Design Patterns', 'modern-database-design-patterns',
'# Modern Database Design Patterns

Database design has evolved significantly with the rise of microservices, cloud computing, and modern application architectures. This guide explores contemporary database design patterns that address today''s scalability and performance challenges.

## Evolution of Database Design

Traditional monolithic applications relied on single, centralized databases. Modern applications require more flexible, scalable approaches that can handle distributed systems and varying data access patterns.

## Key Modern Patterns

### 1. Database per Service
In microservices architecture, each service owns its data:

**Benefits:**
- Service independence
- Technology diversity
- Fault isolation
- Scalability

**Challenges:**
- Data consistency
- Cross-service queries
- Increased complexity

### 2. Event Sourcing
Store all changes as a sequence of events:

```sql
CREATE TABLE events (
  id UUID PRIMARY KEY,
  aggregate_id UUID NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  event_data JSONB NOT NULL,
  version INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

**Benefits:**
- Complete audit trail
- Temporal queries
- Event replay capability
- Natural fit for event-driven architectures

### 3. CQRS (Command Query Responsibility Segregation)
Separate read and write models:

```sql
-- Write model (normalized)
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255) UNIQUE,
  created_at TIMESTAMP
);

-- Read model (denormalized)
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY,
  email VARCHAR(255),
  full_name VARCHAR(255),
  post_count INTEGER,
  last_login TIMESTAMP
);
```

### 4. Polyglot Persistence
Use different databases for different data types:

- **Relational (PostgreSQL)**: Transactional data
- **Document (MongoDB)**: Content management
- **Graph (Neo4j)**: Relationships
- **Time-series (InfluxDB)**: Metrics and logs
- **Cache (Redis)**: Session data

## Data Consistency Patterns

### Saga Pattern
Manage distributed transactions across services:

```typescript
class OrderSaga {
  async execute(order: Order) {
    try {
      await this.reserveInventory(order);
      await this.processPayment(order);
      await this.shipOrder(order);
    } catch (error) {
      await this.compensate(order, error);
    }
  }
}
```

### Eventual Consistency
Accept temporary inconsistency for better availability:

```sql
-- Event table for cross-service communication
CREATE TABLE domain_events (
  id UUID PRIMARY KEY,
  event_type VARCHAR(100),
  aggregate_id UUID,
  payload JSONB,
  published BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Scalability Patterns

### Read Replicas
Distribute read load across multiple database instances:

```sql
-- Master for writes
INSERT INTO posts (title, content) VALUES (?, ?);

-- Replica for reads
SELECT * FROM posts WHERE status = ''published'';
```

### Sharding
Partition data across multiple databases:

```sql
-- Shard by user ID
CREATE TABLE users_shard_1 (
  id UUID PRIMARY KEY CHECK (id::text LIKE ''1%''),
  -- other columns
);

CREATE TABLE users_shard_2 (
  id UUID PRIMARY KEY CHECK (id::text LIKE ''2%''),
  -- other columns
);
```

### Caching Strategies

#### Cache-Aside Pattern
```typescript
async function getUser(id: string): Promise<User> {
  // Try cache first
  let user = await cache.get(`user:${id}`);

  if (!user) {
    // Cache miss - fetch from database
    user = await database.getUser(id);
    await cache.set(`user:${id}`, user, 3600);
  }

  return user;
}
```

#### Write-Through Pattern
```typescript
async function updateUser(id: string, data: UserUpdate): Promise<User> {
  // Update database
  const user = await database.updateUser(id, data);

  // Update cache
  await cache.set(`user:${id}`, user, 3600);

  return user;
}
```

## Performance Optimization

### Indexing Strategies
```sql
-- Composite index for common query patterns
CREATE INDEX idx_posts_status_date ON posts(status, published_at DESC);

-- Partial index for specific conditions
CREATE INDEX idx_active_users ON users(id) WHERE status = ''active'';

-- Expression index for computed values
CREATE INDEX idx_user_email_lower ON users(LOWER(email));
```

### Query Optimization
```sql
-- Use EXPLAIN ANALYZE to understand query performance
EXPLAIN ANALYZE
SELECT p.title, u.name
FROM posts p
JOIN users u ON p.author_id = u.id
WHERE p.status = ''published''
  AND p.published_at > NOW() - INTERVAL ''30 days'';
```

## Security Patterns

### Row-Level Security
```sql
-- Enable RLS
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;

-- Create policy
CREATE POLICY user_posts ON posts
  FOR ALL TO app_user
  USING (author_id = current_user_id());
```

### Data Encryption
```sql
-- Encrypt sensitive data
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255),
  encrypted_ssn BYTEA, -- Encrypted at application level
  created_at TIMESTAMP
);
```

## Monitoring and Observability

### Database Metrics
- Query performance
- Connection pool usage
- Replication lag
- Storage utilization

### Application Metrics
- Cache hit rates
- Transaction success rates
- Data consistency checks

## Best Practices

1. **Design for Failure**: Assume components will fail
2. **Monitor Everything**: Comprehensive observability
3. **Test Data Patterns**: Validate consistency and performance
4. **Document Decisions**: Record architectural choices
5. **Evolve Gradually**: Incremental improvements over big rewrites

## Conclusion

Modern database design requires balancing consistency, availability, and partition tolerance. By understanding and applying these patterns, you can build systems that scale effectively while maintaining data integrity and performance.',
'Explore contemporary database design patterns including microservices data management, event sourcing, and CQRS implementations.',
1, 'published', '/blog-images/web-navigation.jpg', 'en', '2025-01-20 13:00:00', 820, 25);

-- Link posts to categories
INSERT INTO post_categories (post_id, category_id) VALUES
(1, 5), -- Next.js guide -> Web Development
(1, 8), -- Next.js guide -> Next.js
(2, 2), -- React Server Components -> Programming
(2, 7), -- React Server Components -> React
(3, 1), -- AI Tools -> AI
(3, 4), -- AI Tools -> Technology
(4, 9), -- CSS Grid -> CSS
(4, 3), -- CSS Grid -> Tutorials
(5, 2), -- TypeScript -> Programming
(5, 6), -- TypeScript -> JavaScript
(6, 10), -- Database -> Database
(6, 4); -- Database -> Technology

-- Link posts to tags
INSERT INTO post_tags (post_id, tag_id) VALUES
(1, 3), -- Next.js guide -> tutorial
(1, 4), -- Next.js guide -> guide
(1, 1), -- Next.js guide -> beginner
(2, 2), -- React Server Components -> advanced
(2, 6), -- React Server Components -> best-practices
(3, 4), -- AI Tools -> guide
(3, 5), -- AI Tools -> tips
(4, 3), -- CSS Grid -> tutorial
(4, 9), -- CSS Grid -> responsive
(5, 2), -- TypeScript -> advanced
(5, 6), -- TypeScript -> best-practices
(6, 2), -- Database -> advanced
(6, 7); -- Database -> performance
