-- Database indexes for performance optimization

-- Indexes for posts table
CREATE INDEX idx_posts_status_published ON posts(status, published_at DESC);
CREATE INDEX idx_posts_author ON posts(author_id);
CREATE INDEX idx_posts_slug ON posts(slug);
CREATE INDEX idx_posts_language ON posts(language);
CREATE INDEX idx_posts_type ON posts(post_type);

-- Indexes for comments table
CREATE INDEX idx_comments_post ON comments(post_id, status);
CREATE INDEX idx_comments_parent ON comments(parent_id);
CREATE INDEX idx_comments_status ON comments(status);

-- Indexes for categories table
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_parent ON categories(parent_id);

-- Indexes for tags table
CREATE INDEX idx_tags_slug ON tags(slug);

-- Indexes for media table
CREATE INDEX idx_media_uploaded_by ON media(uploaded_by);
CREATE INDEX idx_media_mime_type ON media(mime_type);

-- Indexes for post metadata
CREATE INDEX idx_post_meta_key ON post_meta(post_id, meta_key);

-- Indexes for user sessions
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);

-- Indexes for post views
CREATE INDEX idx_post_views_post_date ON post_views(post_id, viewed_at);

-- Indexes for relationship tables
CREATE INDEX idx_post_categories_category ON post_categories(category_id);
CREATE INDEX idx_post_tags_tag ON post_tags(tag_id);
