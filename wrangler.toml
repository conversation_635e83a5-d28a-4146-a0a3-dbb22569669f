name = "blog-frontend"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# D1 Database configuration
[[d1_databases]]
binding = "DB"
database_name = "blog-database"
database_id = "38d675e4-df5b-4c8a-b6f2-490996d8dd89"

# Environment variables
[vars]
NODE_ENV = "production"

# Development environment
[env.development]
[env.development.vars]
NODE_ENV = "development"

# Production environment  
[env.production]
[env.production.vars]
NODE_ENV = "production"

# Pages configuration for static site deployment
[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

# Local development settings
[dev]
local = true
port = 8787
