#!/usr/bin/env python3
"""
WordPress to Cloudflare Importer using MCP Tools
Imports WordPress database content to Cloudflare D1 while excluding author information
"""

import re
import json
import html
from datetime import datetime

class WordPressMCPImporter:
    def __init__(self):
        self.database_id = "7e82e5e1-b4d3-4607-bf5a-6874e4a4d9b4"  # wordpress-content database
    
    def parse_sql_values(self, values_string):
        """Parse SQL VALUES string into individual values with better handling"""
        values = []
        current_value = ""
        in_quotes = False
        quote_char = None
        paren_depth = 0
        i = 0

        while i < len(values_string):
            char = values_string[i]

            if not in_quotes:
                if char in ["'", '"']:
                    in_quotes = True
                    quote_char = char
                    current_value += char
                elif char == '(':
                    paren_depth += 1
                    current_value += char
                elif char == ')':
                    paren_depth -= 1
                    current_value += char
                elif char == ',' and paren_depth == 0:
                    values.append(current_value.strip())
                    current_value = ""
                else:
                    current_value += char
            else:
                current_value += char
                if char == quote_char:
                    # Check if it's escaped
                    if i + 1 < len(values_string) and values_string[i + 1] == quote_char:
                        i += 1  # Skip the next quote
                        current_value += values_string[i]
                    else:
                        in_quotes = False
                        quote_char = None

            i += 1

        # Add the last value
        if current_value.strip():
            values.append(current_value.strip())

        return values
    
    def clean_text(self, text):
        """Clean and decode text from SQL"""
        if not text or text == 'NULL':
            return ""
        
        # Remove quotes
        text = text.strip("'\"")
        
        # Decode HTML entities
        text = html.unescape(text)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_posts_from_sql(self, sql_file_path):
        """Extract WordPress posts from SQL dump file"""
        print("📖 Extracting posts from SQL dump...")
        posts = []
        
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the wp_posts INSERT statements
        post_pattern = r"INSERT INTO `wp_posts` VALUES\s*\((.*?)\);"
        matches = re.findall(post_pattern, content, re.DOTALL)
        
        for match in matches:
            # Split the values by comma, handling quoted strings
            values = self.parse_sql_values(match)
            
            if len(values) >= 23:  # WordPress posts table has many columns
                post_id = values[0]
                # Skip post_author (values[1]) - excluding author information
                post_date = values[2]
                post_date_gmt = values[3]
                post_content = self.clean_text(values[4])
                post_title = self.clean_text(values[5])
                post_excerpt = self.clean_text(values[6])
                post_status = self.clean_text(values[7])
                comment_status = self.clean_text(values[8])
                ping_status = self.clean_text(values[9])
                post_password = self.clean_text(values[10])
                post_name = self.clean_text(values[11])
                # Skip to_ping and pinged
                post_modified = values[14]
                post_modified_gmt = values[15]
                # Skip post_content_filtered
                post_parent = values[17]
                guid = self.clean_text(values[18])
                menu_order = values[19]
                post_type = self.clean_text(values[20])
                post_mime_type = self.clean_text(values[21])
                comment_count = values[22]
                
                try:
                    posts.append({
                        'id': int(post_id),
                        'post_date': post_date.strip("'\""),
                        'post_date_gmt': post_date_gmt.strip("'\""),
                        'post_content': post_content,
                        'post_title': post_title,
                        'post_excerpt': post_excerpt,
                        'post_status': post_status,
                        'comment_status': comment_status,
                        'ping_status': ping_status,
                        'post_password': post_password,
                        'post_name': post_name,
                        'post_modified': post_modified.strip("'\""),
                        'post_modified_gmt': post_modified_gmt.strip("'\""),
                        'post_parent': int(post_parent) if post_parent.strip("'\"") != 'NULL' and post_parent.strip("'\"").isdigit() else 0,
                        'guid': guid,
                        'menu_order': int(menu_order) if menu_order.strip("'\"") != 'NULL' and menu_order.strip("'\"").isdigit() else 0,
                        'post_type': post_type,
                        'post_mime_type': post_mime_type,
                        'comment_count': int(comment_count) if comment_count.strip("'\"") != 'NULL' and comment_count.strip("'\"").isdigit() else 0
                    })
                except (ValueError, IndexError) as e:
                    print(f"⚠️  Skipping malformed post record: {e}")
                    continue
        
        print(f"✅ Extracted {len(posts)} posts")
        return posts
    
    def extract_comments_from_sql(self, sql_file_path):
        """Extract WordPress comments from SQL dump file (excluding user_id)"""
        print("💬 Extracting comments from SQL dump...")
        comments = []
        
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the wp_comments INSERT statements
        comment_pattern = r"INSERT INTO `wp_comments` VALUES\s*\((.*?)\);"
        matches = re.findall(comment_pattern, content, re.DOTALL)
        
        for match in matches:
            values = self.parse_sql_values(match)
            
            if len(values) >= 15:
                comment_id = values[0]
                comment_post_id = values[1]
                comment_author = self.clean_text(values[2])
                # Skip comment_author_email, comment_author_url, comment_author_IP (values[3-5])
                comment_date = values[6]
                comment_date_gmt = values[7]
                comment_content = self.clean_text(values[8])
                comment_karma = values[9]
                comment_approved = self.clean_text(values[10])
                comment_type = self.clean_text(values[12])
                comment_parent = values[13]
                # Skip user_id (values[14]) - excluding author information
                
                try:
                    comments.append({
                        'id': int(comment_id),
                        'post_id': int(comment_post_id),
                        'comment_author': comment_author,
                        'comment_date': comment_date.strip("'\""),
                        'comment_date_gmt': comment_date_gmt.strip("'\""),
                        'comment_content': comment_content,
                        'comment_karma': int(comment_karma) if comment_karma.strip("'\"") != 'NULL' and comment_karma.strip("'\"").isdigit() else 0,
                        'comment_approved': comment_approved,
                        'comment_type': comment_type,
                        'comment_parent': int(comment_parent) if comment_parent.strip("'\"") != 'NULL' and comment_parent.strip("'\"").isdigit() else 0
                    })
                except (ValueError, IndexError) as e:
                    print(f"⚠️  Skipping malformed comment record: {e}")
                    continue
        
        print(f"✅ Extracted {len(comments)} comments")
        return comments
    
    def extract_terms_from_sql(self, sql_file_path):
        """Extract WordPress terms (categories/tags) from SQL dump file"""
        print("🏷️  Extracting terms from SQL dump...")
        terms = []
        
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the wp_terms INSERT statements
        term_pattern = r"INSERT INTO `wp_terms` VALUES\s*\((.*?)\);"
        matches = re.findall(term_pattern, content, re.DOTALL)
        
        for match in matches:
            values = self.parse_sql_values(match)
            
            if len(values) >= 4:
                term_id = values[0]
                name = self.clean_text(values[1])
                slug = self.clean_text(values[2])
                term_group = values[3]
                
                try:
                    terms.append({
                        'id': int(term_id),
                        'name': name,
                        'slug': slug,
                        'term_group': int(term_group) if term_group.strip("'\"") != 'NULL' and term_group.strip("'\"").isdigit() else 0
                    })
                except (ValueError, IndexError) as e:
                    print(f"⚠️  Skipping malformed term record: {e}")
                    continue
        
        print(f"✅ Extracted {len(terms)} terms")
        return terms
    
    def save_extracted_data(self, posts, comments, terms, filename="extracted_wordpress_data.json"):
        """Save extracted data to JSON file for review"""
        data = {
            'posts': posts[:10],  # Save first 10 for preview
            'comments': comments[:10],
            'terms': terms[:10],
            'summary': {
                'total_posts': len(posts),
                'total_comments': len(comments),
                'total_terms': len(terms),
                'extraction_date': datetime.now().isoformat()
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Saved extracted data preview to {filename}")

if __name__ == "__main__":
    importer = WordPressMCPImporter()
    
    # Extract data from SQL file
    sql_file = "/Users/<USER>/Desktop/SH_WEB/u957990218_GpBKT.zayotech-com.20250727190356.sql"
    
    print("🔍 Starting WordPress data extraction...")
    posts = importer.extract_posts_from_sql(sql_file)
    comments = importer.extract_comments_from_sql(sql_file)
    terms = importer.extract_terms_from_sql(sql_file)
    
    print(f"\n📊 Extraction Summary:")
    print(f"   Posts: {len(posts)}")
    print(f"   Comments: {len(comments)}")
    print(f"   Terms: {len(terms)}")
    
    # Save extracted data for review
    importer.save_extracted_data(posts, comments, terms)
    
    print("\n✅ Data extraction completed!")
    print("📝 Review the extracted_wordpress_data.json file before proceeding with import")
    print("🚀 Next: Use Cloudflare MCP tools to create schema and import data")
