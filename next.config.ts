import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // External packages for server components
  serverExternalPackages: ['@cloudflare/d1'],

  // Output configuration for static export
  output: process.env.NODE_ENV === 'production' ? 'export' : undefined,
  trailingSlash: process.env.NODE_ENV === 'production',

  // Image optimization
  images: {
    unoptimized: process.env.NODE_ENV === 'production', // Use Cloudflare Image Optimization in production
    domains: ['localhost', 'media.yourblog.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

  // Performance optimizations
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react'],
  },

  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Build configuration
  generateBuildId: async () => {
    return 'build-' + Date.now()
  },

  // Security and performance
  poweredByHeader: false,
  reactStrictMode: true,
};

export default nextConfig;
