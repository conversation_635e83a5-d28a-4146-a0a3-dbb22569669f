#!/usr/bin/env python3
"""
Script to extract shayari/poetry content from WordPress database dump
and import it into Cloudflare D1 database
"""

import re
import json
import html
import requests
from datetime import datetime
from urllib.parse import unquote

# Cloudflare API configuration
CLOUDFLARE_ACCOUNT_ID = "ab54ca2d01df4886aa0c3f240ace806d"
DATABASE_ID = "38d675e4-df5b-4c8a-b6f2-490996d8dd89"
API_TOKEN = "your_api_token_here"  # You'll need to set this

def extract_posts_from_sql(sql_file_path):
    """Extract WordPress posts from SQL dump file"""
    posts = []
    
    with open(sql_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the wp_posts INSERT statements
    post_pattern = r"INSERT INTO `wp_posts` VALUES\s*\((.*?)\);"
    matches = re.findall(post_pattern, content, re.DOTALL)
    
    for match in matches:
        # Split the values by comma, handling quoted strings
        values = parse_sql_values(match)
        
        if len(values) >= 23:  # WordPress posts table has many columns
            post_id = values[0]
            post_title = clean_text(values[5])
            post_content = clean_text(values[4])
            post_excerpt = clean_text(values[6])
            post_status = clean_text(values[7])
            post_date = values[2]
            post_name = clean_text(values[12])  # slug
            
            # Only include published posts with shayari-related content
            if (post_status == 'publish' and 
                post_title and 
                ('shayari' in post_title.lower() or 
                 'शायरी' in post_title or
                 'alfaaz' in post_title.lower() or
                 'अलफ़ाज़' in post_title or
                 'poetry' in post_title.lower() or
                 'poem' in post_title.lower() or
                 has_hindi_content(post_content))):
                
                posts.append({
                    'id': int(post_id),
                    'title': post_title,
                    'slug': post_name or f"post-{post_id}",
                    'content': post_content,
                    'excerpt': post_excerpt or generate_excerpt(post_content),
                    'published_at': format_date(post_date),
                    'language': detect_language(post_content, post_title)
                })
    
    return posts

def parse_sql_values(values_string):
    """Parse SQL VALUES string into individual values"""
    values = []
    current_value = ""
    in_quotes = False
    quote_char = None
    i = 0
    
    while i < len(values_string):
        char = values_string[i]
        
        if not in_quotes:
            if char in ["'", '"']:
                in_quotes = True
                quote_char = char
                current_value = ""
            elif char == ',':
                values.append(current_value.strip())
                current_value = ""
            else:
                current_value += char
        else:
            if char == quote_char:
                # Check if it's escaped
                if i + 1 < len(values_string) and values_string[i + 1] == quote_char:
                    current_value += char
                    i += 1  # Skip the next quote
                else:
                    in_quotes = False
                    quote_char = None
            else:
                current_value += char
        
        i += 1
    
    # Add the last value
    if current_value.strip():
        values.append(current_value.strip())
    
    return values

def clean_text(text):
    """Clean and decode text from SQL"""
    if not text or text == 'NULL':
        return ""
    
    # Remove quotes
    text = text.strip("'\"")
    
    # Decode HTML entities
    text = html.unescape(text)
    
    # Remove WordPress shortcodes and HTML
    text = re.sub(r'\[.*?\]', '', text)
    text = re.sub(r'<[^>]+>', '', text)
    
    # Clean up whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def has_hindi_content(text):
    """Check if text contains Hindi/Devanagari characters"""
    if not text:
        return False
    
    # Check for Devanagari Unicode range
    hindi_pattern = r'[\u0900-\u097F]'
    return bool(re.search(hindi_pattern, text))

def detect_language(content, title):
    """Detect language of the content"""
    text = f"{title} {content}"
    
    if has_hindi_content(text):
        return "hi"
    else:
        return "en"

def generate_excerpt(content):
    """Generate excerpt from content"""
    if not content:
        return ""
    
    # Take first 150 characters
    excerpt = content[:150]
    
    # Try to end at a word boundary
    if len(content) > 150:
        last_space = excerpt.rfind(' ')
        if last_space > 100:
            excerpt = excerpt[:last_space] + "..."
    
    return excerpt

def format_date(date_str):
    """Format WordPress date to ISO format"""
    try:
        # WordPress format: 2025-01-30 10:00:00
        dt = datetime.strptime(date_str.strip("'\""), "%Y-%m-%d %H:%M:%S")
        return dt.isoformat() + "Z"
    except:
        return datetime.now().isoformat() + "Z"

def insert_post_to_d1(post):
    """Insert a single post into Cloudflare D1"""
    if not API_TOKEN or API_TOKEN == "your_api_token_here":
        print("Please set your Cloudflare API token")
        return False
    
    query = """
    INSERT INTO posts (
        title, slug, content, excerpt, author_id, status, post_type,
        featured_image_url, language, view_count, comment_count, published_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    params = [
        post['title'],
        post['slug'],
        post['content'],
        post['excerpt'],
        1,  # author_id
        'published',
        'post',
        'https://images.unsplash.com/photo-*************-4e9042af2176?w=600&h=400&fit=crop',
        post['language'],
        0,  # view_count
        0,  # comment_count
        post['published_at']
    ]
    
    url = f"https://api.cloudflare.com/client/v4/accounts/{CLOUDFLARE_ACCOUNT_ID}/d1/database/{DATABASE_ID}/query"
    
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'sql': query,
        'params': params
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            print(f"✅ Inserted: {post['title']}")
            return True
        else:
            print(f"❌ Failed to insert {post['title']}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error inserting {post['title']}: {e}")
        return False

def main():
    sql_file = "/Users/<USER>/Desktop/SH_WEB/u957990218_GpBKT.zayotech-com.**************.sql"
    
    print("🔍 Extracting posts from WordPress database...")
    posts = extract_posts_from_sql(sql_file)
    
    print(f" Found {len(posts)} shayari/poetry posts")
    
    # Show first few posts for preview
    print("\n📋 Preview of posts to import:")
    for i, post in enumerate(posts[:5]):
        print(f"{i+1}. {post['title']} ({post['language']})")
        print(f"   Slug: {post['slug']}")
        print(f"   Excerpt: {post['excerpt'][:100]}...")
        print()
    
    # For now, let's just save to JSON file for review
    with open('extracted_posts.json', 'w', encoding='utf-8') as f:
        json.dump(posts[:20], f, ensure_ascii=False, indent=2)
    
    print(f"💾 Saved first 20 posts to extracted_posts.json for review")
    print("📝 Please review the extracted content and set your API token to import to D1")

if __name__ == "__main__":
    main()
