{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "assist": {"actions": {"source": {"useSortedKeys": "on"}}, "enabled": true}, "files": {"includes": ["!worker-configuration.d.ts"]}, "formatter": {"enabled": true, "indentWidth": 4, "lineWidth": 100}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noInferrableTypes": "error", "noNonNullAssertion": "off", "noParameterAssign": "error", "noUnusedTemplateLiteral": "error", "noUselessElse": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useNumberNamespace": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error"}, "suspicious": {"noConfusingVoidType": "off", "noDebugger": "off", "noExplicitAny": "off"}}}, "root": false, "vcs": {"clientKind": "git", "enabled": true, "useIgnoreFile": true}}