/**
 * Database configuration and connection utilities for Cloudflare D1
 */

// Database configuration
export const DATABASE_CONFIG = {
  // This will be set via environment variables in production
  DATABASE_ID: process.env.DATABASE_ID || '38d675e4-df5b-4c8a-b6f2-490996d8dd89',
  // For development, we'll use Cloudflare D1 via wrangler
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
} as const;

// Type definitions for our database schema
export interface User {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  display_name: string;
  bio?: string;
  avatar_url?: string;
  role: 'subscriber' | 'author' | 'editor' | 'admin';
  status: 'active' | 'inactive' | 'banned';
  email_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parent_id?: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface Tag {
  id: number;
  name: string;
  slug: string;
  description?: string;
  created_at: string;
}

export interface Post {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  author_id: number;
  status: 'draft' | 'published' | 'private' | 'trash';
  post_type: 'post' | 'page';
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  language: 'en' | 'hi';
  view_count: number;
  comment_count: number;
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Comment {
  id: number;
  post_id: number;
  parent_id?: number;
  author_name: string;
  author_email: string;
  author_url?: string;
  author_ip?: string;
  content: string;
  status: 'pending' | 'approved' | 'spam' | 'trash';
  user_agent?: string;
  created_at: string;
  updated_at: string;
}

export interface Media {
  id: number;
  filename: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  width?: number;
  height?: number;
  alt_text?: string;
  caption?: string;
  uploaded_by?: number;
  created_at: string;
}

export interface Setting {
  key: string;
  value?: string;
  autoload: boolean;
  updated_at: string;
}

// Database connection interface
export interface DatabaseConnection {
  prepare(query: string): {
    bind(...params: any[]): {
      all(): Promise<{ results: any[]; success: boolean; meta: any }>;
      first(): Promise<any>;
      run(): Promise<{ success: boolean; meta: any }>;
    };
  };
}



// Real D1 database connection using MCP tools via API
class RealD1Database implements DatabaseConnection {
  private databaseId: string;

  constructor() {
    this.databaseId = process.env.DATABASE_ID || '38d675e4-df5b-4c8a-b6f2-490996d8dd89';
  }

  prepare(query: string) {
    return {
      bind: (...params: any[]) => ({
        all: async () => {
          const results = await this.executeQuery(query, params);
          return { results, success: true, meta: { changes: results.length } };
        },
        first: async () => {
          const results = await this.executeQuery(query, params);
          return results[0] || null;
        },
        run: async () => {
          const results = await this.executeQuery(query, params);
          return {
            success: true,
            meta: {
              changes: results.length,
              last_row_id: results.length > 0 ? results[0]?.id || 0 : 0,
            }
          };
        }
      }),
      all: async () => {
        const results = await this.executeQuery(query, []);
        return { results, success: true, meta: { changes: results.length } };
      },
      first: async () => {
        const results = await this.executeQuery(query, []);
        return results[0] || null;
      },
      run: async () => {
        const results = await this.executeQuery(query, []);
        return {
          success: true,
          meta: {
            changes: results.length,
            last_row_id: results.length > 0 ? results[0]?.id || 0 : 0,
          }
        };
      }
    };
  }

  private async executeQuery(sql: string, params: any[] = []): Promise<any[]> {
    try {
      console.log('🔍 Real D1 query:', sql);
      if (params.length > 0) {
        console.log('📝 Query params:', params);
      }

      // Determine the base URL for API requests
      const baseUrl = typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

      const apiUrl = `${baseUrl}/api/d1-query`;

      // Make API request to our D1 query endpoint
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          databaseId: this.databaseId,
          query: sql,
          params: params
        })
      });

      if (!response.ok) {
        console.error('❌ D1 API request failed:', response.status);
        return [];
      }

      const result = await response.json();
      console.log('✅ D1 API response:', result);

      if (result.success && result.data) {
        return Array.isArray(result.data) ? result.data : [result.data];
      }

      return [];

    } catch (error) {
      console.error('❌ D1 query failed:', error);
      return [];
    }
  }
}

const realD1 = new RealD1Database();

// Get database connection
export function getDatabase(): DatabaseConnection {
  // In production, this will be provided by Cloudflare Workers runtime
  if (typeof globalThis !== 'undefined' && (globalThis as any).DB) {
    return (globalThis as any).DB;
  }

  // For development, use real database connection
  if (typeof process !== 'undefined') {
    console.log('🔗 Using real D1 database connection...');
    return realD1;
  }

  throw new Error('Database not available. Make sure D1 database is properly configured.');
}

// Database utility functions
export class DatabaseUtils {
  private db: DatabaseConnection;

  constructor() {
    this.db = getDatabase();
  }

  // Execute a query with parameters
  async query(sql: string, params: any[] = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...params).all();
      return result;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  // Execute a query and return first result
  async queryFirst(sql: string, params: any[] = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...params).first();
      return result;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  // Execute a query that modifies data
  async execute(sql: string, params: any[] = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...params).run();
      return result;
    } catch (error) {
      console.error('Database execute error:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const db = new DatabaseUtils();
