/**
 * Category service for database operations
 */

import { db } from '../database';
import type { Category } from '../database';

export interface CategoryWithCount extends Category {
  post_count: number;
}

export class CategoryService {
  /**
   * Get all categories with post counts
   */
  async getCategories(): Promise<CategoryWithCount[]> {
    const query = `
      SELECT 
        c.*,
        COUNT(pc.post_id) as post_count
      FROM categories c
      LEFT JOIN post_categories pc ON c.id = pc.category_id
      LEFT JOIN posts p ON pc.post_id = p.id AND p.status = 'published'
      GROUP BY c.id, c.name, c.slug, c.description, c.parent_id, c.sort_order, c.created_at, c.updated_at
      ORDER BY c.sort_order ASC, c.name ASC
    `;

    const result = await db.query(query);
    return result.results || [];
  }

  /**
   * Get a category by slug
   */
  async getCategoryBySlug(slug: string): Promise<Category | null> {
    const query = `
      SELECT * FROM categories 
      WHERE slug = ?
    `;

    const category = await db.queryFirst(query, [slug]);
    return category || null;
  }

  /**
   * Get popular categories (with most posts)
   */
  async getPopularCategories(limit: number = 10): Promise<CategoryWithCount[]> {
    const query = `
      SELECT 
        c.*,
        COUNT(pc.post_id) as post_count
      FROM categories c
      LEFT JOIN post_categories pc ON c.id = pc.category_id
      LEFT JOIN posts p ON pc.post_id = p.id AND p.status = 'published'
      GROUP BY c.id, c.name, c.slug, c.description, c.parent_id, c.sort_order, c.created_at, c.updated_at
      HAVING post_count > 0
      ORDER BY post_count DESC, c.name ASC
      LIMIT ?
    `;

    const result = await db.query(query, [limit]);
    return result.results || [];
  }

  /**
   * Get category hierarchy (parent-child relationships)
   */
  async getCategoryHierarchy(): Promise<(CategoryWithCount & { children?: CategoryWithCount[] })[]> {
    const categories = await this.getCategories();
    
    // Create a map for quick lookup
    const categoryMap = new Map<number, CategoryWithCount & { children?: CategoryWithCount[] }>();
    const rootCategories: (CategoryWithCount & { children?: CategoryWithCount[] })[] = [];

    // Initialize all categories
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Build hierarchy
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!;
      
      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children!.push(categoryWithChildren);
        }
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  }

  /**
   * Create a new category
   */
  async createCategory(data: {
    name: string;
    slug: string;
    description?: string;
    parent_id?: number;
    sort_order?: number;
  }): Promise<Category> {
    const {
      name,
      slug,
      description,
      parent_id,
      sort_order = 0
    } = data;

    const query = `
      INSERT INTO categories (name, slug, description, parent_id, sort_order)
      VALUES (?, ?, ?, ?, ?)
      RETURNING *
    `;

    const result = await db.queryFirst(query, [
      name,
      slug,
      description || null,
      parent_id || null,
      sort_order
    ]);

    return result;
  }

  /**
   * Update a category
   */
  async updateCategory(id: number, data: {
    name?: string;
    slug?: string;
    description?: string;
    parent_id?: number;
    sort_order?: number;
  }): Promise<Category | null> {
    const updates: string[] = [];
    const params: any[] = [];

    if (data.name !== undefined) {
      updates.push('name = ?');
      params.push(data.name);
    }

    if (data.slug !== undefined) {
      updates.push('slug = ?');
      params.push(data.slug);
    }

    if (data.description !== undefined) {
      updates.push('description = ?');
      params.push(data.description);
    }

    if (data.parent_id !== undefined) {
      updates.push('parent_id = ?');
      params.push(data.parent_id);
    }

    if (data.sort_order !== undefined) {
      updates.push('sort_order = ?');
      params.push(data.sort_order);
    }

    if (updates.length === 0) {
      return this.getCategoryById(id);
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `
      UPDATE categories 
      SET ${updates.join(', ')}
      WHERE id = ?
      RETURNING *
    `;

    const result = await db.queryFirst(query, params);
    return result || null;
  }

  /**
   * Delete a category
   */
  async deleteCategory(id: number): Promise<boolean> {
    // First, remove all post associations
    await db.execute('DELETE FROM post_categories WHERE category_id = ?', [id]);

    // Then delete the category
    const result = await db.execute('DELETE FROM categories WHERE id = ?', [id]);
    return result.success;
  }

  /**
   * Get category by ID
   */
  private async getCategoryById(id: number): Promise<Category | null> {
    const query = 'SELECT * FROM categories WHERE id = ?';
    const result = await db.queryFirst(query, [id]);
    return result || null;
  }
}

// Export singleton instance
export const categoryService = new CategoryService();
