/**
 * Services exports
 */

// New database services
export { PostService, postService } from './postService';
export { CategoryService, categoryService } from './categoryService';
export { SettingsService as DatabaseSettingsService, settingsService as databaseSettingsService } from './settingsService';

// Legacy services (keep for compatibility)
// export { BlogService, blogService } from './blog';
export { SettingsService, settingsService } from './settings';

export type {
  PostWithRelations,
  PostsQueryOptions,
} from './postService';

export type {
  CategoryWithCount,
} from './categoryService';

export type {
  SiteSettings as DatabaseSiteSettings,
} from './settingsService';

// Legacy types
// export type {
//   BlogPost,
//   BlogListResponse,
// } from './blog';

export type {
  SiteSettings,
} from './settings';
