/**
 * Post service for database operations
 */

import { db } from '../database';
import type { Post, Category, Tag, User } from '../database';

export interface PostWithRelations extends Post {
  author: {
    display_name: string;
    avatar_url?: string;
  };
  categories: Category[];
  tags?: Tag[];
}

export interface PostsQueryOptions {
  limit?: number;
  offset?: number;
  status?: 'draft' | 'published' | 'private' | 'trash';
  category?: string;
  tag?: string;
  author?: number;
  language?: 'en' | 'hi';
  search?: string;
}

export class PostService {
  /**
   * Get all published posts with pagination and filtering
   */
  async getPosts(options: PostsQueryOptions = {}): Promise<{
    posts: PostWithRelations[];
    total: number;
    hasMore: boolean;
  }> {
    const {
      limit = 12,
      offset = 0,
      status = 'published',
      category,
      tag,
      author,
      language,
      search
    } = options;

    let whereConditions = ['p.status = ?'];
    let params: any[] = [status];

    // Add filters
    if (category) {
      whereConditions.push('EXISTS (SELECT 1 FROM post_categories pc JOIN categories c ON pc.category_id = c.id WHERE pc.post_id = p.id AND c.slug = ?)');
      params.push(category);
    }

    if (tag) {
      whereConditions.push('EXISTS (SELECT 1 FROM post_tags pt JOIN tags t ON pt.tag_id = t.id WHERE pt.post_id = p.id AND t.slug = ?)');
      params.push(tag);
    }

    if (author) {
      whereConditions.push('p.author_id = ?');
      params.push(author);
    }

    if (language) {
      whereConditions.push('p.language = ?');
      params.push(language);
    }

    if (search) {
      whereConditions.push('(p.title LIKE ? OR p.content LIKE ? OR p.excerpt LIKE ?)');
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    const whereClause = whereConditions.join(' AND ');

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM posts p
      WHERE ${whereClause}
    `;
    
    const countResult = await db.queryFirst(countQuery, params);
    const total = countResult?.total || 0;

    // Get posts with author information
    const postsQuery = `
      SELECT 
        p.*,
        u.display_name as author_display_name,
        u.avatar_url as author_avatar_url
      FROM posts p
      JOIN users u ON p.author_id = u.id
      WHERE ${whereClause}
      ORDER BY p.published_at DESC, p.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const postsResult = await db.query(postsQuery, [...params, limit, offset]);
    const posts = postsResult.results || [];

    // Get categories and tags for each post
    const postsWithRelations: PostWithRelations[] = await Promise.all(
      posts.map(async (post: any) => {
        const categories = await this.getPostCategories(post.id);
        const tags = await this.getPostTags(post.id);

        return {
          ...post,
          author: {
            display_name: post.author_display_name,
            avatar_url: post.author_avatar_url,
          },
          categories,
          tags,
        };
      })
    );

    return {
      posts: postsWithRelations,
      total,
      hasMore: offset + limit < total,
    };
  }

  /**
   * Get a single post by slug
   */
  async getPostBySlug(slug: string): Promise<PostWithRelations | null> {
    const query = `
      SELECT 
        p.*,
        u.display_name as author_display_name,
        u.avatar_url as author_avatar_url
      FROM posts p
      JOIN users u ON p.author_id = u.id
      WHERE p.slug = ? AND p.status = 'published'
    `;

    const post = await db.queryFirst(query, [slug]);
    
    if (!post) {
      return null;
    }

    const categories = await this.getPostCategories(post.id);
    const tags = await this.getPostTags(post.id);

    // Increment view count
    await this.incrementViewCount(post.id);

    return {
      ...post,
      author: {
        display_name: post.author_display_name,
        avatar_url: post.author_avatar_url,
      },
      categories,
      tags,
    };
  }

  /**
   * Get categories for a post
   */
  private async getPostCategories(postId: number): Promise<Category[]> {
    const query = `
      SELECT c.*
      FROM categories c
      JOIN post_categories pc ON c.id = pc.category_id
      WHERE pc.post_id = ?
      ORDER BY c.name
    `;

    const result = await db.query(query, [postId]);
    return result.results || [];
  }

  /**
   * Get tags for a post
   */
  private async getPostTags(postId: number): Promise<Tag[]> {
    const query = `
      SELECT t.*
      FROM tags t
      JOIN post_tags pt ON t.id = pt.tag_id
      WHERE pt.post_id = ?
      ORDER BY t.name
    `;

    const result = await db.query(query, [postId]);
    return result.results || [];
  }

  /**
   * Increment view count for a post
   */
  private async incrementViewCount(postId: number): Promise<void> {
    const updateQuery = `
      UPDATE posts 
      SET view_count = view_count + 1 
      WHERE id = ?
    `;

    await db.execute(updateQuery, [postId]);

    // Also record the view in analytics table
    const insertViewQuery = `
      INSERT INTO post_views (post_id, viewed_at)
      VALUES (?, CURRENT_TIMESTAMP)
    `;

    await db.execute(insertViewQuery, [postId]);
  }

  /**
   * Get featured posts
   */
  async getFeaturedPosts(limit: number = 6): Promise<PostWithRelations[]> {
    const query = `
      SELECT 
        p.*,
        u.display_name as author_display_name,
        u.avatar_url as author_avatar_url
      FROM posts p
      JOIN users u ON p.author_id = u.id
      WHERE p.status = 'published'
      ORDER BY p.view_count DESC, p.published_at DESC
      LIMIT ?
    `;

    const result = await db.query(query, [limit]);
    const posts = result.results || [];

    // Get categories for each post
    const postsWithRelations: PostWithRelations[] = await Promise.all(
      posts.map(async (post: any) => {
        const categories = await this.getPostCategories(post.id);

        return {
          ...post,
          author: {
            display_name: post.author_display_name,
            avatar_url: post.author_avatar_url,
          },
          categories,
        };
      })
    );

    return postsWithRelations;
  }

  /**
   * Search posts
   */
  async searchPosts(query: string, limit: number = 10): Promise<PostWithRelations[]> {
    return this.getPosts({
      search: query,
      limit,
      offset: 0,
    }).then(result => result.posts);
  }

  /**
   * Get related posts
   */
  async getRelatedPosts(postId: number, limit: number = 4): Promise<PostWithRelations[]> {
    const query = `
      SELECT DISTINCT
        p.*,
        u.display_name as author_display_name,
        u.avatar_url as author_avatar_url
      FROM posts p
      JOIN users u ON p.author_id = u.id
      JOIN post_categories pc ON p.id = pc.post_id
      WHERE p.status = 'published' 
        AND p.id != ?
        AND pc.category_id IN (
          SELECT category_id 
          FROM post_categories 
          WHERE post_id = ?
        )
      ORDER BY p.published_at DESC
      LIMIT ?
    `;

    const result = await db.query(query, [postId, postId, limit]);
    const posts = result.results || [];

    // Get categories for each post
    const postsWithRelations: PostWithRelations[] = await Promise.all(
      posts.map(async (post: any) => {
        const categories = await this.getPostCategories(post.id);

        return {
          ...post,
          author: {
            display_name: post.author_display_name,
            avatar_url: post.author_avatar_url,
          },
          categories,
        };
      })
    );

    return postsWithRelations;
  }
}

// Export singleton instance
export const postService = new PostService();
