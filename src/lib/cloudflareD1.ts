/**
 * Cloudflare D1 Database Service
 * Uses Cloudflare REST API to interact with D1 database
 */

import { DatabaseConnection } from './database';

export class CloudflareD1Database implements DatabaseConnection {
  private accountId: string;
  private databaseId: string;
  private apiToken: string;
  private baseUrl: string;

  constructor() {
    this.accountId = process.env.CLOUDFLARE_ACCOUNT_ID || 'ab54ca2d01df4886aa0c3f240ace806d';
    this.databaseId = process.env.DATABASE_ID || '38d675e4-df5b-4c8a-b6f2-490996d8dd89';
    this.apiToken = process.env.CLOUDFLARE_API_TOKEN || '';
    this.baseUrl = `https://api.cloudflare.com/client/v4/accounts/${this.accountId}/d1/database/${this.databaseId}`;

    if (!this.accountId || !this.databaseId) {
      throw new Error('Missing Cloudflare configuration. Please set CLOUDFLARE_ACCOUNT_ID and DATABASE_ID environment variables.');
    }
  }

  prepare(query: string) {
    return {
      bind: (...params: any[]) => ({
        all: async () => {
          const results = await this.executeQuery(query, params);
          return { results, success: true, meta: { changes: results.length } };
        },
        first: async () => {
          const results = await this.executeQuery(query, params);
          return results[0] || null;
        },
        run: async () => {
          const results = await this.executeQuery(query, params);
          return {
            success: true,
            meta: {
              changes: results.length,
              last_row_id: results.length > 0 ? results[0]?.id || 0 : 0,
            }
          };
        }
      }),
      all: async () => {
        const results = await this.executeQuery(query, []);
        return { results, success: true, meta: { changes: results.length } };
      },
      first: async () => {
        const results = await this.executeQuery(query, []);
        return results[0] || null;
      },
      run: async () => {
        const results = await this.executeQuery(query, []);
        return {
          success: true,
          meta: {
            changes: results.length,
            last_row_id: results.length > 0 ? results[0]?.id || 0 : 0,
          }
        };
      }
    };
  }

  private async executeQuery(sql: string, params: any[] = []): Promise<any[]> {
    try {
      console.log('🔍 Executing D1 query via API:', sql);
      if (params.length > 0) {
        console.log('📝 Query params:', params);
      }

      // Prepare the request body
      const requestBody = {
        sql: sql,
        params: params
      };

      // Make API request to Cloudflare D1
      const response = await fetch(`${this.baseUrl}/query`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ D1 API request failed:', response.status, errorText);

        // If it's a "no such table" error, return empty results
        if (errorText.includes('no such table') || response.status === 404) {
          console.log('📝 Table does not exist, returning empty results');
          return [];
        }

        throw new Error(`D1 API request failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ D1 API response:', result);

      // Extract results from the API response
      if (result.success && result.result && Array.isArray(result.result)) {
        return result.result;
      } else if (result.result && result.result.results) {
        return result.result.results;
      } else if (result.result) {
        return Array.isArray(result.result) ? result.result : [result.result];
      }

      console.log('✅ Query executed successfully (no data returned)');
      return [];

    } catch (error) {
      console.error('❌ D1 query failed:', error);

      // If it's a network error or API unavailable, return empty results for graceful degradation
      if (error instanceof Error && (
        error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.message.includes('ENOTFOUND')
      )) {
        console.log('📝 Network error, returning empty results for graceful degradation');
        return [];
      }

      throw error;
    }
  }
}

// Export a singleton instance
export const cloudflareD1 = new CloudflareD1Database();
