/**
 * Data transformation utilities
 */

import type { PostWithRelations } from '../services/postService';
import type { BlogPost } from '../../types/blog';
import { calculateReadTime } from './readTime';

/**
 * Transform database post to blog post format
 */
export function transformPostToBlogPost(post: PostWithRelations): BlogPost {
  return {
    id: post.id,
    title: post.title,
    slug: post.slug,
    excerpt: post.excerpt || undefined,
    featured_image_url: post.featured_image_url || undefined,
    published_at: post.published_at || post.created_at,
    view_count: post.view_count,
    comment_count: post.comment_count,
    read_time: calculateReadTime(post.content),
    author: {
      display_name: post.author.display_name,
      avatar_url: post.author.avatar_url || undefined,
    },
    categories: post.categories.map(category => ({
      name: category.name,
      slug: category.slug,
    })),
    language: post.language,
  };
}

/**
 * Transform multiple database posts to blog posts
 */
export function transformPostsToBlogPosts(posts: PostWithRelations[]): BlogPost[] {
  return posts.map(transformPostToBlogPost);
}
