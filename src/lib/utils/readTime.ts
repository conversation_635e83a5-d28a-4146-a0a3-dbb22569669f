/**
 * Calculate estimated read time for content
 */

export function calculateReadTime(content: string): number {
  // Average reading speed is 200-250 words per minute
  // We'll use 225 words per minute as a middle ground
  const wordsPerMinute = 225;
  
  // Remove HTML tags and count words
  const plainText = content.replace(/<[^>]*>/g, '');
  const wordCount = plainText.trim().split(/\s+/).length;
  
  // Calculate read time in minutes, minimum 1 minute
  const readTime = Math.max(1, Math.ceil(wordCount / wordsPerMinute));
  
  return readTime;
}
