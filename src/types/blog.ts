/**
 * Blog-related type definitions
 * These types are used for frontend display and API responses
 */

// Frontend blog post interface for display
export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt?: string;
  featured_image_url?: string;
  published_at: string;
  view_count: number;
  comment_count: number;
  read_time?: number;
  author: {
    display_name: string;
    avatar_url?: string;
  };
  categories: Array<{
    name: string;
    slug: string;
    color?: string;
  }>;
  tags?: Array<{
    name: string;
    slug: string;
  }>;
  language: 'en' | 'hi';
}

// Blog list response interface
export interface BlogListResponse {
  posts: BlogPost[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Category interface for frontend
export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  post_count: number;
}

// Tag interface for frontend
export interface BlogTag {
  id: number;
  name: string;
  slug: string;
  post_count: number;
}
