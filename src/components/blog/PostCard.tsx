import Image from 'next/image';

interface Post {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  featured_image_url?: string;
  published_at: string;
  view_count: number;
  comment_count: number;
  read_time?: number; // in minutes
  author: {
    display_name: string;
    avatar_url?: string;
  };
  categories: Array<{
    name: string;
    slug: string;
    color?: string;
  }>;
  language: 'en' | 'hi';
}

export interface PostCardProps {
  post: Post;
  variant?: 'default' | 'featured' | 'compact';
  showImage?: boolean;
}

export function PostCard({
  post,
  variant = 'default',
  showImage = true
}: PostCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getOptimizedImageUrl = (url: string, width: number = 600, height: number = 400) => {
    if (!url) return '';
    // Use Cloudflare Image Optimization or fallback
    return url.startsWith('http') ? url : `/blog-images/${url}`;
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'AI': 'bg-purple-100 text-purple-800',
      'Programming': 'bg-blue-100 text-blue-800',
      'Tutorials': 'bg-green-100 text-green-800',
      'Technology': 'bg-indigo-100 text-indigo-800',
      'Web Development': 'bg-cyan-100 text-cyan-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <article className="bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group border border-gray-100 hover-lift animate-fade-in">
      {/* Featured Image */}
      {showImage && post.featured_image_url && (
        <div className="relative aspect-[16/10] overflow-hidden">
          <Image
            src={getOptimizedImageUrl(post.featured_image_url)}
            alt={`Featured image for ${post.title}`}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={false}
            loading="lazy"
          />

          {/* Category Tag Overlay */}
          {post.categories.length > 0 && (
            <div className="absolute top-4 left-4">
              <a
                href={`/category/${post.categories[0].slug}/`}
                className={`inline-block px-3 py-1 rounded-full text-xs font-medium transition-colors ${getCategoryColor(post.categories[0].name)}`}
              >
                {post.categories[0].name}
              </a>
            </div>
          )}
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {/* Title */}
        <h2 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
          <a
            href={`/posts/${post.slug}/`}
            className="focus-ring rounded"
            aria-label={`Read article: ${post.title}`}
          >
            {post.title}
          </a>
        </h2>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
            {post.excerpt}
          </p>
        )}

        {/* Footer with author and meta */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center space-x-3">
            {/* Author Avatar */}
            <div className="relative">
              {post.author.avatar_url ? (
                <Image
                  src={post.author.avatar_url}
                  alt={post.author.display_name}
                  width={32}
                  height={32}
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium text-blue-600">
                  {post.author.display_name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>

            {/* Author Name */}
            <span className="text-sm font-medium text-gray-700">
              {post.author.display_name}
            </span>
          </div>

          {/* Read Time */}
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{post.read_time || 3} Min Read</span>
          </div>
        </div>
      </div>
    </article>
  );
}
