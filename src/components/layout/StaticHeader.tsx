export function StaticHeader() {
  return (
    <header className="bg-white shadow-sm sticky top-0 z-40 border-b border-gray-100">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex items-center">
            <a href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">T</span>
              </div>
              <div className="hidden sm:block">
                <span className="text-xl font-bold text-gray-900">Teknorial</span>
                <div className="text-xs text-gray-500 -mt-1">Technology & Tutorials</div>
              </div>
            </a>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <a href="/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Home
            </a>
            <a href="/category/ai/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              AI
            </a>
            <a href="/category/programming/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Programming
            </a>
            <a href="/category/tutorials/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Tutorials
            </a>
            <a href="/about/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              About
            </a>
          </nav>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            {/* Search button */}
            <a
              href="/search/"
              className="p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Search"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </a>

            {/* Mobile menu button - simplified for static version */}
            <div className="lg:hidden">
              <button
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors"
                aria-label="Menu"
                disabled
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu - always visible for static version */}
      <div className="lg:hidden border-t border-gray-100 bg-white">
        <nav className="container mx-auto px-4 py-4">
          <div className="flex flex-col space-y-3">
            <a href="/" className="text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors">
              Home
            </a>
            <a href="/category/ai/" className="text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors">
              AI
            </a>
            <a href="/category/programming/" className="text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors">
              Programming
            </a>
            <a href="/category/tutorials/" className="text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors">
              Tutorials
            </a>
            <a href="/about/" className="text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors">
              About
            </a>
          </div>
        </nav>
      </div>
    </header>
  );
}
